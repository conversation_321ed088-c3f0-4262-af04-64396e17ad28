import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Platform,
  Image,
  ImageBackground,
  StyleSheet,
  Animated,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { HomeStackParamList, RootStackParamList } from '../../navigation/types';
import SearchBar from '../../components/common/SearchBar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

type Props = NativeStackScreenProps<HomeStackParamList, 'Home'>;
type RootNavigationProp = NativeStackNavigationProp<RootStackParamList>;

// 工具项的类型定义
interface ToolItem {
  id: string;
  name: string;
  icon: any;
  onPress: () => void;
  badge?: string;
}

const HomeScreen: React.FC<Props> = () => {
  const [activeTab, setActiveTab] = useState('company'); // 'company' 或 'bidding'
  const [activeDataTab, setActiveDataTab] = useState('package'); // 'package' 或 'ranking'

  const navigation = useNavigation<RootNavigationProp>();

  // 动画相关
  const tabIndicatorPosition = useRef(new Animated.Value(0)).current;

  // 切换tab时的动画
  const switchTab = (tab: string) => {
    setActiveTab(tab);

    // 计算指示器位置
    const targetPosition = tab === 'company' ? 0 : 1;

    Animated.timing(tabIndicatorPosition, {
      toValue: targetPosition,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  // 热搜词标签
  const hotSearchTags = [
    { id: '1', name: '招投' },
    { id: '2', name: '装修' },
    { id: '3', name: '体检' },
    { id: '4', name: '银行' },
    { id: '5', name: '医保' },
    { id: '6', name: '智慧医院' },
  ];

  // 企业查询工具数据
  const companyToolItems: ToolItem[] = [
    {
      id: '1',
      name: '查产品',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('SearchResult', { type: 'product' }),
    },
    {
      id: '2',
      name: '查业绩',
      icon: require('@assets/home/<USER>'),
      onPress: () =>
        navigation.navigate('SearchResult', { type: 'performance' }),
    },
    {
      id: '3',
      name: '查人员',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('SearchResult', { type: 'person' }),
    },
    {
      id: '4',
      name: '查资质',
      icon: require('@assets/home/<USER>'),
      onPress: () =>
        navigation.navigate('SearchResult', { type: 'qualification' }),
    },
    {
      id: '5',
      name: '查专利',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('SearchResult', { type: 'patent' }),
    },
    {
      id: '6',
      name: '更多工具',
      icon: require('@assets/home/<USER>'),
      onPress: () => {},
      badge: '即将上线',
    },
  ];

  // 标讯工具数据
  const biddingToolItems: ToolItem[] = [
    {
      id: '1',
      name: '查标讯',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('BidNotice'),
    },
    {
      id: '2',
      name: '查业主',
      icon: require('@assets/home/<USER>'),
      onPress: () => navigation.navigate('OwnerSearch'),
    },
    {
      id: '3',
      name: '查渠道',
      icon: require('@assets/home/<USER>'),
      onPress: () => {},
    },
    {
      id: '4',
      name: '查代理',
      icon: require('@assets/home/<USER>'),
      onPress: () => {},
    },
  ];

  // 潜客数据包
  const dataPackages = [
    {
      id: '1',
      title: '科技型中小企业',
      description: '有科技型中小企业认定的企业',
      count: '10000',
      type: 'tech',
    },
    {
      id: '2',
      title: '科技型中小企业',
      description: '有科技型中小企业认定的企业',
      count: '10000',
      type: 'tech',
    },
    {
      id: '3',
      title: '科技型中小企业',
      description: '有科技型中小企业认定的企业',
      count: '10000',
      type: 'tech',
    },
    {
      id: '4',
      title: '科技型中小企业',
      description: '有科技型中小企业认定的企业',
      count: '10000',
      type: 'tech',
    },
  ];

  // 企业榜单数据
  const companyRankings = [
    {
      id: '1',
      title: '失信被执行人企业',
      description: '有失信被执行人的企业',
      count: '100w+',
    },
    {
      id: '2',
      title: '失信被执行人企业',
      description: '有失信被执行人的企业',
      count: '100w+',
    },
    {
      id: '3',
      title: '失信被执行人企业',
      description: '有失信被执行人的企业',
      count: '100w+',
    },
    {
      id: '4',
      title: '失信被执行人企业',
      description: '有失信被执行人的企业',
      count: '100w+',
    },
  ];

  // 最新标讯数据
  const latestBiddings = [
    {
      id: '1',
      title:
        '北京亿达腾飞科技有限公司北京亿达腾飞腾飞科技有限公司腾飞科技有腾飞...',
      type: '中标公告',
      amount: '20000元',
      bidder: '2个联系人',
      company: 'xxx有限责任公司（自然人投资或控股）',
      location: '湖南 长沙',
      date: '10:00',
    },
    {
      id: '2',
      title:
        '北京亿达腾飞科技有限公司北京亿达腾飞腾飞科技有限公司腾飞科技有腾飞...',
      type: '招标公告',
      amount: '20000元',
      company: 'xxx有限责任公司（自然人投资或控股）',
      agent: 'xxx有限责任公司（自然人投资或控股）',
      location: '湖南 长沙',
      date: '2025-05-05',
    },
  ];

  // 渲染工具区域
  const renderToolsSection = () => {
    if (activeTab === 'company') {
      // 企业查询工具 - 原有样式
      return (
        <ImageBackground
          source={require('@assets/home/<USER>')}
          style={styles.toolsContainer}
          resizeMode="stretch"
        >
          <View style={styles.toolsContent}>
            {/* 企业查询工具图片 */}
            <View style={styles.companyToolsImageContainer}>
              <Image
                source={require('@assets/home/<USER>')}
                style={styles.companyToolsImage}
              />
              <Text style={styles.companyToolsText}>企业查询工具</Text>
            </View>
            {/* 工具网格 */}
            <View style={styles.toolsGrid}>
              {companyToolItems.map(item => (
                <TouchableOpacity
                  key={item.id}
                  style={styles.toolItem}
                  onPress={item.onPress}
                >
                  <View style={styles.toolIconContainer}>
                    <Image
                      source={item.icon}
                      style={styles.toolIcon}
                      resizeMode="contain"
                    />
                    {item.badge && (
                      <View style={styles.badge}>
                        <Text style={styles.badgeText}>{item.badge}</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.toolName}>{item.name}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ImageBackground>
      );
    } else {
      // 标讯查询工具 - 新样式
      return (
        <View style={styles.biddingToolsContainer}>
          <View style={styles.biddingToolsRow}>
            {biddingToolItems.map(item => (
              <TouchableOpacity
                key={item.id}
                style={styles.biddingToolItem}
                onPress={item.onPress}
              >
                <View style={styles.biddingToolIconContainer}>
                  <Image
                    source={item.icon}
                    style={styles.biddingToolIcon}
                    resizeMode="contain"
                  />
                </View>
                <Text style={styles.biddingToolName}>{item.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      );
    }
  };

  // 渲染数据切换区域
  const renderDataTabsSection = () => {
    return (
      <View style={styles.dataTabsContainer}>
        <View style={styles.dataTabsWrapper}>
          <TouchableOpacity
            style={[
              styles.dataTabButton,
              activeDataTab === 'ranking' && styles.leftTabOffset,
              activeDataTab === 'package' && styles.activeTabButton,
            ]}
            onPress={() => setActiveDataTab('package')}
          >
            <ImageBackground
              source={
                activeDataTab === 'package'
                  ? require('@assets/home/<USER>')
                  : require('@assets/home/<USER>')
              }
              style={[
                styles.dataTabImage,
                activeDataTab === 'package'
                  ? styles.activeDataTabImage
                  : styles.inactiveDataTabImage,
              ]}
              resizeMode="stretch"
            >
              <Text
                style={[
                  styles.dataTabText,
                  activeDataTab === 'package' && styles.activeDataTabText,
                ]}
              >
                潜客数据包
              </Text>
            </ImageBackground>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.dataTabButton,
              activeDataTab === 'package' && styles.rightTabOffset,
              activeDataTab === 'ranking' && styles.activeTabButton,
            ]}
            onPress={() => setActiveDataTab('ranking')}
          >
            <ImageBackground
              source={
                activeDataTab === 'ranking'
                  ? require('@assets/home/<USER>')
                  : require('@assets/home/<USER>')
              }
              style={[
                styles.dataTabImage,
                activeDataTab === 'ranking'
                  ? styles.activeDataTabImage
                  : styles.inactiveDataTabImage,
              ]}
              resizeMode="stretch"
            >
              <Text
                style={[
                  styles.dataTabText,
                  activeDataTab === 'ranking' && styles.activeDataTabText,
                ]}
              >
                企业榜单
              </Text>
            </ImageBackground>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // 渲染数据内容
  const renderDataContent = () => {
    if (activeDataTab === 'package') {
      return (
        <View style={styles.dataContentContainer}>
          {dataPackages.map(item => (
            <TouchableOpacity key={item.id} style={styles.dataItem}>
              <View style={styles.dataItemContent}>
                <Text style={styles.dataItemTitle}>{item.title}</Text>
                <Text style={styles.dataItemDescription}>
                  {item.description}
                </Text>
                <Text style={styles.dataItemCount}>
                  企业数量:{' '}
                  <Text style={styles.dataItemCountNumber}>{item.count}</Text>
                </Text>
              </View>
              <Text style={styles.dataItemArrow}>{'>'}</Text>
            </TouchableOpacity>
          ))}
        </View>
      );
    } else {
      return (
        <View style={styles.dataContentContainer}>
          {companyRankings.map(item => (
            <TouchableOpacity key={item.id} style={styles.dataItem}>
              <View style={styles.dataItemContent}>
                <Text style={styles.dataItemTitle}>{item.title}</Text>
                <Text style={styles.dataItemDescription}>
                  {item.description}
                </Text>
                <Text style={styles.dataItemCount}>
                  企业数量:{' '}
                  <Text style={styles.dataItemCountNumber}>{item.count}</Text>
                </Text>
              </View>
              <Text style={styles.dataItemArrow}>{'>'}</Text>
            </TouchableOpacity>
          ))}
        </View>
      );
    }
  };

  // 渲染标讯内容
  const renderBiddingContent = () => {
    return (
      <View style={styles.biddingContentContainer}>
        {/* 筛选区域 */}
        <View style={styles.filterContainer}>
          <View style={styles.filterRow}>
            {/* 左侧标签带渐变装饰条 */}
            <View style={styles.filterLabelContainer}>
              <LinearGradient
                colors={['#8ea8ff', '#4b74ff']}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
                style={styles.filterLabelBorder}
              />
              <View style={styles.filterLabel}>
                <Text style={styles.filterLabelText}>最新标讯</Text>
              </View>
            </View>

            {/* 右侧筛选按钮 */}
            <View style={styles.filterButtons}>
              <TouchableOpacity style={styles.filterDropdown}>
                <Text style={styles.filterDropdownText}>信息类型</Text>
                <Image
                  source={require('@assets/icon_arrow_bottom.png')}
                  style={styles.filterDropdownArrow}
                  resizeMode="contain"
                />
              </TouchableOpacity>
              <TouchableOpacity style={styles.filterDropdown}>
                <Text style={styles.filterDropdownText}>地区</Text>
                <Image
                  source={require('@assets/icon_arrow_bottom.png')}
                  style={styles.filterDropdownArrow}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* 标讯列表 */}
        <View style={styles.biddingListContainer}>
          {latestBiddings.map(item => (
            <TouchableOpacity key={item.id} style={styles.biddingItem}>
              <Text style={styles.biddingTitle} numberOfLines={2}>
                {item.title}
              </Text>
              <View style={styles.biddingTags}>
                <View style={[styles.biddingTag, styles.biddingTypeTag]}>
                  <Text style={styles.biddingTagText}>{item.type}</Text>
                </View>
                {item.amount && (
                  <View style={[styles.biddingTag, styles.biddingAmountTag]}>
                    <Text style={styles.biddingTagText}>{item.amount}</Text>
                  </View>
                )}
                {item.bidder && (
                  <View style={[styles.biddingTag, styles.biddingBidderTag]}>
                    <Text style={styles.biddingTagText}>{item.bidder}</Text>
                  </View>
                )}
              </View>
              <Text style={styles.biddingCompany} numberOfLines={1}>
                招标单位: {item.company}
              </Text>
              {item.agent && (
                <Text style={styles.biddingAgent} numberOfLines={1}>
                  代理单位: {item.agent}
                </Text>
              )}
              <View style={styles.biddingFooter}>
                <Text style={styles.biddingLocation}>{item.location}</Text>
                <Text style={styles.biddingDate}>{item.date}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  // 获取状态栏高度
  const statusBarHeight =
    Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;

  // 动态样式
  const dynamicStyles = StyleSheet.create({
    headerContainerDynamic: {
      paddingTop: statusBarHeight + 20,
    },
  });

  return (
    <ScrollView style={styles.container} bounces={false}>
      <StatusBar barStyle="light-content" />

      {/* 顶部背景区域 */}
      <ImageBackground
        source={require('@assets/home/<USER>')}
        style={[styles.headerContainer, dynamicStyles.headerContainerDynamic]}
        resizeMode="cover"
      >
        {/* Tab切换 */}
        <View style={styles.tabContainer}>
          <View style={styles.tabWrapper}>
            <TouchableOpacity
              style={styles.tab}
              onPress={() => switchTab('company')}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === 'company' && styles.activeTabText,
                ]}
              >
                查企业
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.tab}
              onPress={() => switchTab('bidding')}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === 'bidding' && styles.activeTabText,
                ]}
              >
                查标讯
              </Text>
            </TouchableOpacity>

            {/* 渐变指示器 */}
            <Animated.View
              style={[
                styles.tabIndicatorContainer,
                {
                  transform: [
                    {
                      translateX: tabIndicatorPosition.interpolate({
                        inputRange: [0, 1],
                        outputRange: [30, 110], // 简化的居中位置
                      }),
                    },
                  ],
                },
              ]}
            >
              <LinearGradient
                colors={['#ffffff', '#4b74ff']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.tabIndicator}
              />
            </Animated.View>
          </View>
        </View>

        {/* 搜索框 */}
        <View style={styles.searchBarContainer}>
          <SearchBar
            value=""
            onChangeText={() => {}}
            onSearch={() => {}}
            placeholder={
              activeTab === 'company'
                ? '请输入名称、关键词'
                : '请输入项目名称、项目编号等关键词'
            }
            onPressSearchBar={() => {
              if (activeTab === 'company') {
                navigation.navigate('CompanySearch');
              } else {
                navigation.navigate('BidNoticeSearch', { keyword: '' });
              }
            }}
          />
        </View>
        {/* 热搜词 */}
        <View style={styles.hotSearchContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.hotSearchScroll}
            contentContainerStyle={styles.hotSearchScrollContent}
          >
            <Image
              source={require('@assets/home/<USER>')}
              style={styles.hotSearchIcon}
              resizeMode="contain"
            />
            {hotSearchTags.map(tag => (
              <TouchableOpacity key={tag.id} style={[styles.hotSearchTag]}>
                <Text style={[styles.hotSearchTagText]}>{tag.name}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ImageBackground>

      {/* 工具区域 */}
      {renderToolsSection()}

      {/* 根据activeTab显示不同内容 */}
      {activeTab === 'company' ? (
        <>
          {/* 数据切换区域 */}
          {renderDataTabsSection()}

          {/* 数据内容区域 */}
          {renderDataContent()}

          {/* 查看更多按钮 */}
          <TouchableOpacity style={styles.moreButton}>
            <Text style={styles.moreButtonText}>查看更多 {'>'}</Text>
          </TouchableOpacity>
        </>
      ) : (
        <>
          {/* 标讯内容区域 */}
          {renderBiddingContent()}
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerContainer: {
    paddingBottom: 12,
    height: 253,
  },
  tabContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 18,
  },
  tabWrapper: {
    flexDirection: 'row',
    position: 'relative',
    alignItems: 'center',
  },
  tab: {
    marginHorizontal: 16,
    paddingBottom: 8,
    paddingTop: 4,
  },
  tabIndicatorContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
  },
  tabIndicator: {
    width: 33,
    height: 5,
    borderRadius: 3,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#ffffff',
  },
  tabText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '500',
  },
  activeTabText: {
    fontWeight: '600',
  },
  hotSearchContainer: {},
  searchBarContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  hotSearchScroll: {
    paddingHorizontal: 12,
  },
  hotSearchScrollContent: {
    alignItems: 'center',
  },
  hotSearchTag: {
    marginRight: 12,
  },

  hotSearchTagText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FFFFFF',
    lineHeight: 12,
  },

  toolsContainer: {
    marginHorizontal: 12,
    marginTop: -55,
    borderRadius: 12,
    overflow: 'hidden',
  },
  toolsContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  companyToolsImageContainer: {
    position: 'relative',
    marginRight: 16,
  },
  companyToolsImage: {
    width: 80,
    height: 150,
  },
  companyToolsText: {
    position: 'absolute',
    top: 15,
    left: 11,
    fontSize: 14,
    fontWeight: '600',
    color: '#4B74FF',
    width: 58,
    lineHeight: 20,
  },
  toolsGrid: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    alignContent: 'space-between',
    height: 160,
  },
  toolItem: {
    width: '32%',
    height: '48%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  toolIconContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  toolIcon: {
    width: 48,
    height: 48,
  },
  badge: {
    position: 'absolute',
    top: -2,
    left: 12,
    backgroundColor: '#FF3535',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderTopLeftRadius: 6,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    borderBottomLeftRadius: 0,
    overflow: 'hidden',
  },
  badgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: '500',
  },
  toolName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#363B4D',
  },
  dataTabsContainer: {
    marginHorizontal: 16,
    marginTop: 12,
  },
  dataTabsWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  dataTabButton: {
    flex: 1,
  },
  activeTabButton: {
    zIndex: 10,
  },
  leftTabOffset: {
    marginRight: -20,
  },
  rightTabOffset: {
    marginLeft: -20,
  },
  dataTabImage: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeDataTabImage: {
    height: 49,
  },
  inactiveDataTabImage: {
    height: 40,
  },
  dataTab: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  activeDataTab: {
    backgroundColor: '#3b82f6',
  },
  dataTabText: {
    fontSize: 16,
    color: '#363B4D',
  },
  activeDataTabText: {
    fontWeight: '600',
  },
  dataContentContainer: {
    marginHorizontal: 16,
    marginTop: 12,
  },
  dataItem: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  dataItemContent: {
    flex: 1,
  },
  dataItemTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  dataItemDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
  },
  dataItemCount: {
    fontSize: 14,
    color: '#6b7280',
  },
  dataItemCountNumber: {
    color: '#ef4444',
    fontWeight: '600',
  },
  dataItemArrow: {
    fontSize: 18,
    color: '#9ca3af',
    marginLeft: 16,
  },
  moreButton: {
    marginHorizontal: 16,
    marginVertical: 24,
    backgroundColor: '#ffffff',
    borderRadius: 24,
    paddingVertical: 16,
    alignItems: 'center',
  },
  moreButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6b7280',
  },
  hotSearchIcon: {
    width: 44,
    height: 20,
    marginRight: 8,
  },
  // 标讯工具样式
  biddingToolsContainer: {
    marginHorizontal: 12,
    marginTop: -50,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
  },
  biddingToolsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  biddingToolItem: {
    alignItems: 'center',
    flex: 1,
  },
  biddingToolIconContainer: {
    width: 35,
    height: 35,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 9,
  },
  biddingToolIcon: {
    width: 35,
    height: 35,
  },
  biddingToolName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    textAlign: 'center',
  },
  // 标讯相关样式
  biddingContentContainer: {
    marginHorizontal: 16,
  },
  filterContainer: {
    marginVertical: 17,
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  filterLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterLabelBorder: {
    width: 4,
    height: 15,
    borderTopLeftRadius: 50,
    borderTopRightRadius: 0,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 50,
    marginRight: 8,
  },
  filterLabel: {
    paddingVertical: 8,
    borderRadius: 6,
  },
  filterLabelText: {
    color: '#323232',
    fontSize: 15,
    fontWeight: '500',
  },
  filterButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterDropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 6,
    marginLeft: 20,
  },
  filterDropdownText: {
    fontSize: 14,
    color: '#333333',
    marginRight: 8,
  },
  filterDropdownArrow: {
    width: 6,
    height: 4,
  },
  biddingListContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
  },
  biddingItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  biddingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    lineHeight: 22,
    marginBottom: 8,
  },
  biddingTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  biddingTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 4,
  },
  biddingTypeTag: {
    backgroundColor: '#FF6B35',
  },
  biddingAmountTag: {
    backgroundColor: '#4F7CFF',
  },
  biddingBidderTag: {
    backgroundColor: '#52C41A',
  },
  biddingTagText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '500',
  },
  biddingCompany: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  biddingAgent: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  biddingFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  biddingLocation: {
    fontSize: 12,
    color: '#999999',
  },
  biddingDate: {
    fontSize: 12,
    color: '#999999',
  },
});

export default HomeScreen;
