/**
 * 应用导航配置
 * 定义整个应用的导航结构和路由配置
 */

import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

// 导入TabIcon组件
import TabIcon from '@components/navigation/TabIcon';

// 导入导航工具
import { navigationRef } from '@navigation/rootNavigation';

// 导入状态管理
import { useAuth, useAuthActions } from '@stores/userStore';

// 导入类型定义
import {
  RootStackParamList,
  TabParamList,
  AuthStackParamList,
  GraphStackParamList,
  ProfileStackParamList,
  DetailStackParamList,
  FollowStackParamList,
  HomeStackParamList,
} from '@navigation/types';

// 导入页面组件
// 首页
/**
 * 首页
 * 提供应用主要功能入口和概览
 */
import HomeScreen from '@screens/home/<USER>';

// Auth 页面
/**
 * 一键登录页面
 * 提供手机号一键登录功能，简化用户登录流程
 */
import OneClickLoginScreen from '@screens/auth/OneClickLoginScreen';

/**
 * 登录页面
 * 用户账号密码登录界面
 */
import LoginScreen from '@screens/auth/LoginScreen';

/**
 * 注册页面
 * 新用户注册账号界面
 */
import RegisterScreen from '@screens/auth/RegisterScreen';

/**
 * 验证码页面
 * 接收并验证短信验证码，用于登录、注册和重置密码流程
 */
import VerificationCodeScreen from '@screens/auth/VerificationCodeScreen';

/**
 * 忘记密码页面
 * 提供用户找回密码的入口
 */
import ForgotPasswordScreen from '@screens/auth/ForgotPasswordScreen';

/**
 * 重置密码页面
 * 验证成功后重新设置密码
 */
import ResetPasswordScreen from '@screens/auth/ResetPasswordScreen';

// 导入搜索相关页面
import {
  SearchScreen,
  SearchResultScreen,
  SearchFilterScreen,
  CompanySearchScreen,
  BidNoticeSearchScreen,
  BidNoticeFilterScreen,
  OwnerSearchScreen,
} from '@screens/search';

// Home/Customer 页面
/**
 * 客户页面
 * 提供客户搜索和管理功能的主页面
 */
// import CustomerScreen from '../screens/customer/CustomerScreen';

// Graph/Search 页面
/**
 * 企业图谱页面
 * 展示企业关系网络和图谱可视化
 */
import GraphScreen from '@screens/graph/GraphScreen';

/**
 * 企业搜索页面
 * 专门用于企业搜索的入口
 */
// import CompanySearchScreen from '../screens/search/company/CompanySearchScreen';

/**
 * 搜索结果页面
 * 展示搜索结果列表，支持多种筛选和排序
 */
// import SearchResultScreen from '../screens/search/SearchResultScreen';

/**
 * 搜索筛选页面
 * 提供高级筛选选项，精确定位搜索结果
 */
// import SearchFilterScreen from '../screens/search/SearchFilterScreen';

/**
 * 企业列表页面
 * 展示企业列表，支持按分类查看
 */
import CompanyListScreen from '@screens/company/CompanyListScreen';

/**
 * 产品列表页面
 * 展示产品列表，支持按分类查看
 */
import ProductListScreen from '@screens/product/ProductListScreen';

// Profile 页面
/**
 * 个人资料页面
 * 展示和管理用户个人信息
 */
import ProfileScreen from '@screens/profile/ProfileScreen';
import TabsDemoScreen from '@screens/profile/TabsDemoScreen';
import RegionSelectorDemoScreen from '@screens/profile/RegionSelectorDemoScreen';
import RegionPopoverDemoScreen from '@screens/profile/RegionPopoverDemoScreen';

/**
 * 设置页面
 * 应用各种设置选项
 */
import SettingsScreen from '@screens/profile/SettingsScreen';

/**
 * 订单列表页面
 * 展示用户的购买订单历史
 */
import OrdersScreen from '@screens/profile/OrdersScreen';

/**
 * 关于页面
 * 应用介绍和公司信息
 */
import AboutScreen from '@screens/profile/AboutScreen';

/**
 * 帮助中心页面
 * 常见问题和使用帮助
 */
import HelpScreen from '@screens/profile/HelpScreen';

/**
 * 用户协议页面
 * 显示应用的用户协议
 */
import UserAgreementScreen from '@screens/profile/UserAgreementScreen';

/**
 * 免责声明页面
 * 显示应用的免责声明
 */
import DisclaimerScreen from '@screens/profile/DisclaimerScreen';

// Detail 页面
/**
 * 企业详情页面
 * 展示企业的详细信息，包括基本信息、资质、业绩等
 */
import CompanyDetailScreen from '@screens/detail/CompanyDetailScreen';

/**
 * 企业图谱详情页面
 * 展示特定企业的关系网络和图谱可视化
 */
import CompanyGraphScreen from '@screens/detail/CompanyGraphScreen';

/**
 * 资质详情页面
 * 展示企业资质证书的详细信息
 */
import QualificationDetailScreen from '@screens/detail/QualificationDetailScreen';

/**
 * 业绩详情页面
 * 展示企业业绩项目的详细信息
 */
import PerformanceDetailScreen from '@screens/detail/PerformanceDetailScreen';

/**
 * 专利详情页面
 * 展示专利的详细信息，包括专利类型、申请状态等
 */
import PatentDetailScreen from '@screens/detail/PatentDetailScreen';

/**
 * 人员详情页面
 * 展示人员的详细信息，包括职位、资质等
 */
import PersonDetailScreen from '@screens/detail/PersonDetailScreen';

/**
 * 产品详情页面
 * 展示产品的详细信息和特性
 */
import ProductDetailScreen from '@screens/detail/ProductDetailScreen';

// 关注页面
/**
 * 关注列表页面
 * 展示用户关注的企业、人员、项目等
 */
import FollowsScreen from '@screens/follow/FollowsScreen';

/**
 * 关注设置页面
 * 管理关注通知和更新频率等设置
 */
import FollowSettingsScreen from '@screens/follow/FollowSettingsScreen';

/** 临时占位组件 - 后续需要实现 */
// Customer 相关临时组件
/**
 * 招标公告页面
 * 展示招标信息列表，支持筛选和查看详情
 */
import BidNoticeScreen from '@screens/bidding/BidNoticeScreen';

/**
 * 招标公告搜索页面
 * 提供针对招标公告的专项搜索功能
 */
// import BidNoticeSearchScreen from '../screens/search/bidding/BidNoticeSearchScreen';

/**
 * 招标公告筛选页面
 * 提供针对招标公告的高级筛选选项
 */
// import BidNoticeFilterScreen from '../screens/bidding/BidNoticeFilterScreen';

/**
 * 业主搜索页面
 * 提供针对项目业主的搜索功能
 */
// import OwnerSearchScreen from '../screens/bidding/OwnerSearchScreen';

// Graph 相关临时组件
/**
 * 搜索历史页面
 * 展示用户历史搜索记录
 */
const SearchHistoryScreen: React.FC<any> = () => null;

// Profile 相关临时组件
/**
 * 编辑用户名页面
 * 修改用户显示名称
 */
const EditNameScreen: React.FC<any> = () => null;

/**
 * 优惠券列表页面
 * 展示用户的可用优惠券
 */
const CouponsScreen: React.FC<any> = () => null;

/**
 * 优惠券支付页面
 * 使用优惠券进行服务购买
 */
const CouponPaymentScreen: React.FC<any> = () => null;

/**
 * 客服页面
 * 提供在线客服咨询服务
 */
const CustomerServiceScreen: React.FC<any> = () => null;

/**
 * 潜在客户列表页面
 * 展示系统推荐的潜在客户名单
 */
const PotentialCustomerListScreen: React.FC<any> = () => null;

/**
 * 潜在客户套餐页面
 * 展示和购买潜在客户推荐服务
 */
const PotentialCustomerPackageScreen: React.FC<any> = () => null;

// Detail 相关临时组件
/**
 * 企业联系信息页面
 * 展示企业联系人和联系方式
 */
const CompanyContactScreen: React.FC<any> = () => null;

/**
 * 业绩商务信息页面
 * 展示项目业绩的商务信息，如合同金额等
 */
const PerformanceBusinessInfoScreen: React.FC<any> = () => null;

/**
 * 业绩相关单位页面
 * 展示与特定业绩相关的参与单位
 */
const PerformanceRelatedUnitScreen: React.FC<any> = () => null;

/**
 * 专利进度页面
 * 展示专利申请和审批进度详情
 */
const PatentProgressScreen: React.FC<any> = () => null;

/**
 * 招标详情页面
 * 展示招标公告的详细信息
 */
import BidDetailScreen from '@screens/bidding/BidDetailScreen';

// Follow 相关临时组件
/**
 * 关注详情页面
 * 展示特定关注项的详细信息和更新动态
 */
const FollowDetailScreen: React.FC<any> = () => null;

/**
 * 创建导航栈
 */
const RootStack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();
const AuthStack = createNativeStackNavigator<AuthStackParamList>();
const GraphStack = createNativeStackNavigator<GraphStackParamList>();
const ProfileStack = createNativeStackNavigator<ProfileStackParamList>();
const DetailStack = createNativeStackNavigator<DetailStackParamList>();
const FollowStack = createNativeStackNavigator<FollowStackParamList>();
const CustomerStack = createNativeStackNavigator<HomeStackParamList>();

// 类型断言帮助函数，用于处理组件类型与路由参数的兼容
type ComponentType = React.ComponentType<any>;

/**
 * Auth 栈导航
 * 处理用户登录、注册和密码管理相关的页面导航
 */
const AuthNavigator = () => {
  return (
    <AuthStack.Navigator screenOptions={{ headerShown: false }}>
      <AuthStack.Screen
        name="OneClickLogin"
        component={OneClickLoginScreen as ComponentType}
      />
      <AuthStack.Screen name="Login" component={LoginScreen as ComponentType} />
      <AuthStack.Screen
        name="Register"
        component={RegisterScreen as ComponentType}
      />
      <AuthStack.Screen
        name="VerificationCode"
        component={VerificationCodeScreen as ComponentType}
      />
      <AuthStack.Screen
        name="ForgotPassword"
        component={ForgotPasswordScreen as ComponentType}
      />
      <AuthStack.Screen
        name="ResetPassword"
        component={ResetPasswordScreen as ComponentType}
      />
    </AuthStack.Navigator>
  );
};

/**
 * 找客户栈导航
 * 管理客户搜索、招标信息相关的页面导航
 */
const CustomerNavigator = () => {
  return (
    <CustomerStack.Navigator screenOptions={{ headerShown: false }}>
      <CustomerStack.Screen
        name="Home"
        component={HomeScreen as ComponentType}
      />
      <CustomerStack.Screen
        name="BidNotice"
        component={BidNoticeScreen as ComponentType}
      />
    </CustomerStack.Navigator>
  );
};

/**
 * 企业图谱栈导航
 * 管理企业图谱、搜索和各类型列表页面导航
 */
const GraphNavigator = () => {
  return (
    <GraphStack.Navigator screenOptions={{ headerShown: false }}>
      <GraphStack.Screen
        name="Graph"
        component={GraphScreen as ComponentType}
      />
      <GraphStack.Screen
        name="CompanyList"
        component={CompanyListScreen as ComponentType}
      />
      <GraphStack.Screen
        name="ProductList"
        component={ProductListScreen as ComponentType}
      />
      <GraphStack.Screen
        name="SearchHistory"
        component={SearchHistoryScreen as ComponentType}
      />
    </GraphStack.Navigator>
  );
};

/**
 * 关注栈导航
 * 管理用户关注列表、详情和设置页面导航
 */
const FollowNavigator = () => {
  return (
    <FollowStack.Navigator screenOptions={{ headerShown: false }}>
      <FollowStack.Screen
        name="Follows"
        component={FollowsScreen as ComponentType}
      />
      <FollowStack.Screen
        name="FollowDetail"
        component={FollowDetailScreen as ComponentType}
      />
      <FollowStack.Screen
        name="FollowSettings"
        component={FollowSettingsScreen as ComponentType}
      />
    </FollowStack.Navigator>
  );
};

/**
 * 个人中心栈导航
 * 管理用户个人信息、设置和服务相关的页面导航
 */
const ProfileNavigator = () => {
  return (
    <ProfileStack.Navigator screenOptions={{ headerShown: false }}>
      <ProfileStack.Screen
        name="Profile"
        component={ProfileScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="EditName"
        component={EditNameScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="Orders"
        component={OrdersScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="Coupons"
        component={CouponsScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="CouponPayment"
        component={CouponPaymentScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="CustomerService"
        component={CustomerServiceScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="About"
        component={AboutScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="Help"
        component={HelpScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="UserAgreement"
        component={UserAgreementScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="Disclaimer"
        component={DisclaimerScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="PotentialCustomerList"
        component={PotentialCustomerListScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="PotentialCustomerPackage"
        component={PotentialCustomerPackageScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="Follows"
        component={FollowsScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="TabsDemo"
        component={TabsDemoScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="RegionSelectorDemo"
        component={RegionSelectorDemoScreen as ComponentType}
      />
      <ProfileStack.Screen
        name="RegionPopoverDemo"
        component={RegionPopoverDemoScreen as ComponentType}
      />
    </ProfileStack.Navigator>
  );
};

/**
 * 详情栈导航
 * 管理各类实体（企业、人员、专利等）详情页面的导航
 */
const DetailNavigator = () => {
  return (
    <DetailStack.Navigator screenOptions={{ headerShown: false }}>
      <DetailStack.Screen
        name="CompanyDetail"
        component={CompanyDetailScreen as ComponentType}
      />
      <DetailStack.Screen
        name="CompanyGraph"
        component={CompanyGraphScreen as ComponentType}
      />
      <DetailStack.Screen
        name="CompanyContact"
        component={CompanyContactScreen as ComponentType}
      />
      <DetailStack.Screen
        name="ProductDetail"
        component={ProductDetailScreen as ComponentType}
      />
      <DetailStack.Screen
        name="QualificationDetail"
        component={QualificationDetailScreen as ComponentType}
      />
      <DetailStack.Screen
        name="PerformanceDetail"
        component={PerformanceDetailScreen as ComponentType}
      />
      <DetailStack.Screen
        name="PerformanceBusinessInfo"
        component={PerformanceBusinessInfoScreen as ComponentType}
      />
      <DetailStack.Screen
        name="PerformanceRelatedUnit"
        component={PerformanceRelatedUnitScreen as ComponentType}
      />
      <DetailStack.Screen
        name="PatentDetail"
        component={PatentDetailScreen as ComponentType}
      />
      <DetailStack.Screen
        name="PatentProgress"
        component={PatentProgressScreen as ComponentType}
      />
      <DetailStack.Screen
        name="PersonDetail"
        component={PersonDetailScreen as ComponentType}
      />
      <DetailStack.Screen
        name="BidDetail"
        component={BidDetailScreen as ComponentType}
      />
    </DetailStack.Navigator>
  );
};

/**
 * Tab 导航
 * 底部标签栏导航，包含主要功能模块
 */
const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: '#3B82F6',
        tabBarInactiveTintColor: '#9CA3AF',
      }}
    >
      <Tab.Screen
        name="HomeTab"
        component={CustomerNavigator as ComponentType}
        options={{
          tabBarLabel: '找客户',
          tabBarIcon: ({ focused, size }) => (
            <TabIcon name="home" focused={focused} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="GraphTab"
        component={GraphNavigator as ComponentType}
        options={{
          tabBarLabel: '企业图谱',
          tabBarIcon: ({ focused, size }) => (
            <TabIcon name="graph" focused={focused} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="FollowTab"
        component={FollowNavigator as ComponentType}
        options={{
          tabBarLabel: '关注',
          tabBarIcon: ({ focused, size }) => (
            <TabIcon name="follow" focused={focused} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="ProfileTab"
        component={ProfileNavigator as ComponentType}
        options={{
          tabBarLabel: '我的',
          tabBarIcon: ({ focused, size }) => (
            <TabIcon name="profile" focused={focused} size={size} />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

/**
 * 根导航
 * 应用主导航容器，管理整个应用的导航状态
 */
const RootNavigator = () => {
  const { isLoggedIn } = useAuth();
  const { initializeAuth } = useAuthActions();

  // 初始化认证状态
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  return (
    <NavigationContainer ref={navigationRef}>
      <RootStack.Navigator
        screenOptions={{ headerShown: false }}
        initialRouteName={isLoggedIn ? 'Main' : 'Auth'}
      >
        <RootStack.Screen
          name="Auth"
          component={AuthNavigator as ComponentType}
        />
        <RootStack.Screen
          name="Main"
          component={TabNavigator as ComponentType}
        />
        <RootStack.Screen
          name="Detail"
          component={DetailNavigator as ComponentType}
        />
        <RootStack.Screen
          name="Settings"
          component={SettingsScreen as ComponentType}
        />
        {/* 将搜索相关页面移到根导航中，使其可以从任何地方访问 */}
        <RootStack.Screen
          name="Search"
          component={SearchScreen as ComponentType}
        />
        <RootStack.Screen
          name="CompanySearch"
          component={CompanySearchScreen as ComponentType}
        />
        <RootStack.Screen
          name="SearchResult"
          component={SearchResultScreen as ComponentType}
        />
        <RootStack.Screen
          name="SearchFilter"
          component={SearchFilterScreen as ComponentType}
        />
        <RootStack.Screen
          name="BidNoticeSearch"
          component={BidNoticeSearchScreen as ComponentType}
        />
        <RootStack.Screen
          name="BidNoticeFilter"
          component={BidNoticeFilterScreen as ComponentType}
        />
        <RootStack.Screen
          name="OwnerSearch"
          component={OwnerSearchScreen as ComponentType}
        />
        <RootStack.Screen
          name="BidDetail"
          component={BidDetailScreen as ComponentType}
        />
      </RootStack.Navigator>
    </NavigationContainer>
  );
};

export default RootNavigator;
